<!-- 批量修改字段 -->
<template>
  <div>
    <el-dialog
      title="批量修改字段"
      v-model="bitchUpdateFieldDialogCopy"
      width="90%"
      :close-on-click-modal="false"
      @opened="init"
      :before-close="handleClose"
      append-to-body
    >
      <el-form :model="formMsg" ref="formMsgRef" :rules="formMsgRule" label-width="100px" label-position="top">
        <div class="dialog-row">
          <div class="dialog-left">
            <div class="notice">
              <span
                >注意事项：批量修改字段只适用于选择的该批数据的字段值都是一样的情况，且不允许修改表达式字段以及修改的字段如果影响其他字段（如：其他字段用表达式引用了）请自行刷新表达式！！！</span
              >
              <br />
              <span>如果您选择的数据不是同一个根节点，可能导致数据无法更新，请注意一次只能选择同一根节点的数据！！！</span>
            </div>
            <el-form-item label="数据选择" prop="zdList">
              <el-input type="textarea" :autosize="{ minRows: 24, maxRows: 8 }" v-model="formMsg.zdListNames" @click="chooseData" readonly> </el-input>
            </el-form-item>
          </div>
          <div class="dialog-right">
            <el-form-item label="属性组/字段选择" prop="tableList">
              <div class="add-btn">
                <el-link type="danger" :disabled="multipleSelection.length == 0" @click="delTable" style="margin-right: 10px">删除</el-link>
                <el-link type="primary" @click="addTable" style="margin-right: 10px">新增修改字段</el-link>
                <el-link type="success" @click="bitchAdd">批量增加字段选择</el-link>
              </div>
              <el-table :data="formMsg.tableList" :height="tableHeight" style="width: 100%" @selection-change="handleSelectionChange" border>
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column type="index" width="60" label="序号"></el-table-column>
                <el-table-column prop="linkId" label="属性组">
                  <template #default="scope">
                    <el-select
                      filterable
                      v-model="scope.row.linkId"
                      placeholder="请选择属性组"
                      style="width: 100%"
                      clearable
                      @change="chooseAttr($event, scope.$index)"
                    >
                      <el-option v-for="item in attrList" :key="item.linkId" :label="item.label" :disabled="item.disabled" :value="item.linkId">
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="fieldName" label="字段">
                  <template #default="scope">
                    <el-select
                      filterable
                      v-model="scope.row.fieldName"
                      placeholder="请选择字段"
                      style="width: 100%"
                      clearable
                      @change="changeField($event, scope.$index)"
                    >
                      <el-option
                        v-for="item in scope.row.filedList"
                        :key="item.fieldName"
                        :label="item.fieldCn"
                        :disabled="item.disabled"
                        :value="item.fieldName"
                      >
                      </el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="fieldValue" label="修改的值">
                  <template #default="scope">
                    <el-select
                      filterable
                      v-model="scope.row.fieldValue"
                      placeholder="请选择"
                      v-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'select'"
                      clearable
                    >
                      <el-option v-for="item in scope.row.fieldObj.attribution.options" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                    <el-date-picker
                      v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'date'"
                      v-model="scope.row.fieldValue"
                      :type="scope.row.fieldObj.attribution.type"
                      value-format="x"
                      :placeholder="scope.row.fieldObj.inputHint"
                      style="width: 100%"
                    >
                    </el-date-picker>

                    <el-date-picker
                      v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'date-range'"
                      v-model="scope.row.fieldValue"
                      type="daterange"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 90%"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                    <el-time-picker
                      v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'time'"
                      v-model="scope.row.fieldValue"
                      :picker-options="{
                        selectableRange: '00:00:00 - 23:59:59'
                      }"
                      value-format="HH:mm:ss"
                      :placeholder="scope.row.fieldObj.inputHint"
                      style="width: 100%"
                    >
                    </el-time-picker>
                    <el-input
                      v-else-if="scope.row.fieldObj && ['input', 'textarea', 'number'].includes(scope.row.fieldObj.valueMethod)"
                      :type="scope.row.fieldObj.valueMethod === 'input' ? 'text' : scope.row.fieldObj.valueMethod"
                      v-model="scope.row.fieldValue"
                      style="width: 100%"
                      clearable
                    ></el-input>
                    <el-radio-group v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'radio'" v-model="scope.row.fieldValue">
                      <el-radio :value="item.value" v-for="(item, index) in scope.row.fieldObj.attribution.options" :key="index">{{
                        item.label
                      }}</el-radio>
                    </el-radio-group>

                    <el-checkbox-group v-else-if="scope.row.fieldObj && scope.row.fieldObj.valueMethod == 'checkbox'" v-model="scope.row.fieldValue">
                      <el-checkbox
                        v-for="(item, index) in scope.row.fieldObj.attribution.options"
                        :key="index"
                        :value="item.value"
                        :label="item.label"
                      />
                    </el-checkbox-group>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-link type="danger" @click="deleteRow(scope.$index)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 筛选数据 -->
    <searchData
      :searchDialog="searchDialog"
      @closeSearchDialog="closeSearchDialog"
      @getChooseData="getChooseData"
      :zdList="formMsg.zdList"
      :isManager="false"
      :ifTree="false"
      :isKJ="false"
      :ruleTree="ruleTree"
      :ruleIds="[]"
    ></searchData>
    <!-- 批量选择字段弹窗 -->
    <el-dialog
      title="批量选择字段"
      v-model="bitchFieldDialog"
      width="800px"
      v-dialogDrag
      :close-on-click-modal="false"
      :before-close="handleCloseBitchFieldDialog"
    >
      <div style="padding: 20px 0px">
        <el-select filterable v-model="chooseGroup" placeholder="请选择属性组" style="width: 100%" clearable @change="chooseAttr">
          <el-option v-for="item in attrList" :key="item.linkId" :label="item.label" :disabled="item.disabled" :value="item.linkId"> </el-option>
        </el-select>
        <el-select filterable v-model="chooseFields" multiple placeholder="请选择字段" style="width: 100%; margin-top: 10px" clearable>
          <el-option v-for="item in filedList" :key="item.fieldName" :label="item.fieldCn" :disabled="item.disabled" :value="item.fieldName">
          </el-option>
        </el-select>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseBitchFieldDialog">取 消</el-button>
          <el-button type="primary" @click="submitBitchFields">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 进度弹窗 -->
    <el-dialog title="修改字段进度" v-model="progressDialog" width="300px" :close-on-click-modal="false" :before-close="handleProgressClose">
      <div class="down-dialog">
        <el-progress :stroke-width="16" type="circle" :percentage="progress" :status="editStatus"></el-progress>
        <div style="margin-top: 10px">{{ editMsg }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import searchData from './searchData/index.vue';
import { updateField } from '@/api/project';

// ---Props---
interface Props {
  // 打开弹框
  bitchUpdateFieldDialog: boolean;
  // 模块id
  ruleTree?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  bitchUpdateFieldDialog: false,
  ruleTree: () => []
});

const bitchUpdateFieldDialogCopy = computed(() => props.bitchUpdateFieldDialog);

//--- 定义变量 ---
const formMsg: any = ref({
  zdList: [], //需要回退的数据
  zdListNames: [],
  tableList: [] //修改的属性组内容
});

const formMsgRule = ref({
  zdList: [{ required: true, message: '请选择数据', trigger: 'change' }],
  tableList: [{ required: true, message: '请选择修改的内容', trigger: 'change' }]
});

const ifTree = ref(false);
const searchDialog = ref(false);
const attrList = ref([]); //所有属性组
const filedList = ref([]); //所有字段
const tableHeight = ref(window.innerHeight - 300); //表格高度
const bitchFieldDialog = ref(false); //批量选择字段弹窗
const chooseGroup = ref(''); //批量选择字段的属性组
const chooseFields = ref([]); //批量选择字段的字段
const multipleSelection = ref([]); //表格选中的行
const progressDialog = ref(false); //进度弹窗
const progress = ref(0); //进度
const editMsg = ref(''); //进度信息
const editStatus = ref(''); //进度状态
const okUpdataNum = ref(0); //成功修改的数量
const formMsgRef = ref();

// 定义emit
const emit = defineEmits<{
  (e: 'handleCloseBitchUpdateFieldDialog'): void;
}>();

// --- 定义方法 ---
const init = () => {
  attrList.value = [];
  disposeAttr(JSON.parse(JSON.stringify(props.ruleTree)));
};

/**
 * 组装所有属性组
 * @param list
 */
const disposeAttr = (list) => {
  list.forEach((v) => {
    v.fieldGroupModelList.forEach((k) => {
      k.label = `${k.typeName}(${v.typeName})`;
      attrList.value.push(k);
    });
    if (v.list.length != 0) {
      disposeAttr(v.list);
    }
  });
};

const handleClose = () => {
  // 初始化
  formMsg.value = {
    zdList: [], //需要回退的数据
    zdListNames: [],
    tableList: [] //修改的属性组内容
  };
  filedList.value = []; //所有字段
  emit('handleCloseBitchUpdateFieldDialog');
};

/**
 * 选择数据
 */
const chooseData = () => {
  ifTree.value = false;
  searchDialog.value = true;
};
/**
 * 关闭筛选数据弹窗
 */
const closeSearchDialog = () => {
  formMsg.value.zdList = [];
  formMsg.value.zdListNames = '';
  searchDialog.value = false;
};

/**
 * 得到筛选要素数据
 */
const getChooseData = (list) => {
  formMsg.value.zdList = list;
  const names: any = [];
  list.forEach((v) => {
    names.push(v.parcelName);
  });
  formMsg.value.zdListNames = names.join(',');
  searchDialog.value = false;
};

/**
 * @params {number} e
 * @param {number} index 没有index的代表是批量增加的
 */
const chooseAttr = (e, index) => {
  let filedListTemp = [];
  for (let i = 0; i < attrList.value.length; i++) {
    if (attrList.value[i].linkId == e) {
      filedListTemp = attrList.value[i].fieldModelList;
      break;
    }
  }
  if (filedListTemp.length != 0) {
    filedListTemp.forEach((v) => {
      if (
        [
          'idCardScan',
          'xttable',
          'xtzwsb',
          'xtdwsb',
          'xtvideo',
          'xtfj',
          'cascader',
          'upload',
          'xtzw',
          'xtqm',
          'area',
          'xtaudio',
          'xtsjjt',
          'xtsjy',
          'xtsjy',
          'xtzsdl',
          'xtcy',
          'xtpay'
        ].includes(v.valueMethod) ||
        (v.attribution && v.attribution.expression)
      ) {
        //只有普通属性组才可以选择
        v.disabled = true;
      }
    });
  }
  if ((index != undefined && index != null && index != '') || index == 0) {
    formMsg.value.tableList[index].filedList = filedListTemp;
  } else {
    //代表是批量增加的
    filedList.value = filedListTemp;
  }
};

// 提交数据
const submit = async () => {
  formMsgRef.value.validate((valid) => {
    if (valid) {
      //判断表格中是否有字段值为空的
      let flag = true;
      for (let i = 0; i < formMsg.value.tableList.length; i++) {
        if (!formMsg.value.tableList[i].linkId) {
          ElMessage({
            message: `第${i + 1}行的属性组不能为空`,
            type: 'error'
          });
          flag = false;
          break;
        }
        if (!formMsg.value.tableList[i].fieldName) {
          ElMessage({
            message: `第${i + 1}行的字段不能为空`,
            type: 'error'
          });
          flag = false;
          break;
        }
        if (
          formMsg.value.tableList[i].fieldValue == '' &&
          formMsg.value.tableList[i].fieldValue != 0 &&
          formMsg.value.tableList[i].fieldValue != false
        ) {
          ElMessage({
            message: `第${i + 1}行的字段值不能为空`,
            type: 'error'
          });
          flag = false;
          break;
        }
      }
      if (!flag) {
        return;
      }
      ElMessageBox.confirm('确定要批量修改选择的数据的字段内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 按照 linkId 对 formMsg.tableList 进行分组
          const groups: any = []; //需要修改的属性组
          formMsg.value.tableList.forEach((item) => {
            if (!groups[item.linkId]) {
              groups[item.linkId] = [];
            }
            groups[item.linkId].push(item);
          });
          // 此时 groups 就是分组后的结果，可以根据需求继续处理
          const endGroups = []; //最终需要修改的属性组
          groups.forEach((v) => {
            const obj: any = {
              linkId: v[0].linkId,
              attribution: {}
            };
            v.forEach((k) => {
              if (k.fieldObj.valueMethod === 'checkbox') {
                obj.attribution[k.fieldName] = k.fieldValue.join(',');
              } else if (k.fieldObj.valueMethod === 'date-range') {
                obj.attribution[k.fieldName] = k.fieldValue.join('至');
              } else {
                obj.attribution[k.fieldName] = k.fieldValue;
              }
            });
            endGroups.push(obj);
          });
          const submitList = [];
          const subIds: any = [];
          formMsg.value.zdList.forEach((v) => {
            subIds.push(v.id);
          });
          // 把subIds分割成10个一组的数据
          const subIdsArr = [];
          for (let i = 0; i < subIds.length; i += 10) {
            subIdsArr.push(subIds.slice(i, i + 10));
          }
          let submitCount = 0;
          subIdsArr.forEach((v) => {
            const ite_list = [];
            v.forEach((k) => {
              endGroups.forEach((q) => {
                ite_list.push({
                  linkId: q.linkId,
                  attribution: q.attribution,
                  parcelId: k
                });
                submitCount++;
              });
            });
            submitList.push(ite_list);
          });
          const batchSize = 10;
          let currentIndex = 0;
          progressDialog.value = true;
          progress.value = 0;
          editMsg.value = '数据处理中，当前更新0/0条';
          editStatus.value = '';
          okUpdataNum.value = 0;
          let bitch_index = 0;
          const processBatch = async () => {
            try {
              // 调用接口修改数据
              await editFiled(submitList[bitch_index]);
              okUpdataNum.value = okUpdataNum.value + submitList[bitch_index].length;
              // 更新进度
              progress.value = ((okUpdataNum.value / submitCount) * 100).toFixed(2);
              editMsg.value = `数据处理中，当前更新${okUpdataNum.value}/${submitCount}条`;
              bitch_index++;
              if (bitch_index < submitList.length) {
                // 继续处理下一批次
                await processBatch();
              } else {
                // 全部处理完成
                if (progress.value >= 100) {
                  ElMessageBox.alert('修改成功', '提示', {
                    confirmButtonText: '确定',
                    callback: (action) => {
                      // 在这添加是否切换公司的标识。
                      // 如果等于true 之后 则要移除标识。否则全部页面刷新的时候会再次跳转到首页index页面
                      // 除了在navbar.vue 中的值为true,其他值都为false,也就是如果需要跳转到首页的情况，值为true,反之 值为false
                      sessionStorage.setItem('qiehuan_company', false);
                      location.reload();
                    }
                  });
                }
              }
            } catch (error) {
              ElMessage({
                message: '批量处理出错，请稍后重试',
                type: 'error'
              });
            }
          };
          // 开始处理第一批数据
          processBatch();
        })
        .catch(() => {});
    } else {
      return false;
    }
  });
};

/**
 * 分布调用接口修改
 * @param parmas
 */
const editFiled = async (parmas) => {
  return new Promise((resolve, reject) => {
    // 调用接口
    updateField(parmas).then((res) => {
      if (res.code == 200) {
        resolve(null);
      } else {
        reject(res.msg);
      }
    });
  });
};

/**
 * 删除
 * @param {number} index
 */
const deleteRow = (index) => {
  ElMessageBox.confirm('确定要删除该条内容吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      formMsg.value.tableList.splice(index, 1);
    })
    .catch(() => {});
};

/**
 * 新增字段修改
 */
const addTable = () => {
  formMsg.value.tableList.push({
    linkId: '',
    fieldName: '',
    fieldValue: '',
    fieldObj: null,
    filedList: []
  });
};

/**
 * 选择字段
 */
const changeField = (e, index) => {
  for (let i = 0; i < formMsg.value.tableList[index].filedList.length; i++) {
    if (formMsg.value.tableList[index].filedList[i].fieldName == e) {
      formMsg.value.tableList[index].fieldObj = formMsg.value.tableList[index].filedList[i];
      formMsg.value.tableList[index].fieldValue = formMsg.value.tableList[index].filedList[i].valueMethod === 'checkbox' ? [] : '';

      break;
    }
  }
};

/**
 * 批量增加字段选择
 */
const bitchAdd = () => {
  bitchFieldDialog.value = true;
};
/**
 * 关闭批量增加字段选择弹窗
 */
const handleCloseBitchFieldDialog = () => {
  bitchFieldDialog.value = false;
};

/**
 * 提交批量增加字段选择
 */
const submitBitchFields = () => {
  chooseFields.value.forEach((v) => {
    for (let i = 0; i < filedList.value.length; i++) {
      if (filedList.value[i].fieldName == v) {
        formMsg.value.tableList.push({
          linkId: chooseGroup.value,
          fieldName: v,
          fieldValue: filedList.value[i].valueMethod === 'checkbox' ? [] : '',
          fieldObj: filedList.value[i],
          filedList: filedList.value
        });
        break;
      }
    }
  });
  bitchFieldDialog.value = false;
};

/**
 * 表格多选
 * @param val
 */
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};

/**
 * 删除表格选中的行
 * @param val
 */
const delTable = () => {
  ElMessageBox.confirm('确定要删除所选择的字段吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      multipleSelection.value.forEach((v) => {
        const index = formMsg.value.tableList.indexOf(v);
        if (index != -1) {
          formMsg.value.tableList.splice(index, 1);
        }
      });
      ElMessage({
        type: 'success',
        message: '删除成功!'
      });
    })
    .catch(() => {});
};

const handleProgressClose = () => {
  progressDialog.value = false;
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 10px 20px 0px 20px;
}
.notice {
  color: red;
}
.add-btn {
  position: absolute;
  top: -37px;
  left: 125px;
}
.dialog-row {
  display: flex;
  .dialog-left {
    flex: 1;
  }
  .dialog-right {
    flex: 3;
    margin-left: 20px;
  }
}
.down-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}
</style>
