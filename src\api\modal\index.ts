import { dataScope } from './../system/role/index';
import { param } from './../../utils/index';
// 模块管理 相关的接口
import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import JSONbig from 'json-bigint'; // 解决超过 16 位数字精度丢失问题
import { ModuleData, ModuleQuery, RuleData, ToolData, FieldGroupData, ExportData, ModuleInfo } from '@/api/modal/types';

/**
 * 上传剪裁的图标的接口
 * @param data 上传数据
 * @returns {AxiosPromise}
 */
export function uploadCopperImg(data: FormData): AxiosPromise<any> {
  return request({
    url: '/qjt/file/multi/upload',
    method: 'post',
    data: data
  });
}

/**
 * 新建/修改模块的规则--采集设置调用的接口-把第一部的基础设置中的id带入
 * @param data 规则数据
 * @param param 模块id
 * @returns {AxiosPromise}
 */
export function addRule(data: RuleData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: '/qjt/rule/operaRule',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 新建/修改模块
 * @param data 模块数据
 * @returns {AxiosPromise}
 */
export function addModule(data: ModuleData): AxiosPromise<any> {
  return request({
    url: '/qjt/rule/addModule',
    method: 'post',
    data: data
  });
}

/**
 * 查看已发布和未发布的数据
 * @param params 状态参数数组
 * @param companyId 公司ID
 * @param param 模块id
 * @returns {AxiosPromise}
 */
export function getModuleList(params: any[], companyId?: string | number, param?: ModuleData): AxiosPromise<any> {
  let url = `/qjt/rule/selectModuleList`;
  for (let index = 0; index < params.length; index++) {
    if (index == 0) {
      url = `${url}?status=${params[index]}`;
    } else {
      url = `${url}&status=${params[index]}`;
    }
  }
  if (companyId && companyId != '' && companyId != null) {
    url = `${url}&companyId=${companyId}`;
  }

  return request({
    url: url,
    method: 'post',
    params: param
  });
}

/**
 * 修改模块 --列表中 停用 启用 发布 编辑，删除功能调用的接口
 * @param params 模块数据
 * @param param 模块id
 * @returns {AxiosPromise}
 */
export function modifyModule(data: ModuleData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: '/qjt/rule/modifyModule',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 查看规则或者指定查看规则-将id作为moduleid
 * @param params 查询参数
 * @param param 模块id
 * @returns {AxiosPromise}
 */
export function selectRules(data: ModuleQuery, param?: ModuleInfo): AxiosPromise<any> {
  return request({
    url: '/qjt/rule/selectRules',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 查看工具包列表
 * @param data 查询参数
 * @param param 模块id
 * @returns {AxiosPromise}
 */
export function selectTools(data: ModuleQuery, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: '/qjt/rule/selectTool',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 添加工具包的详细功能 操作工具包详情(如果id为空是添加，否则是修改)
 * @param data 工具包数据
 * @param param 模块id
 * @returns {AxiosPromise}
 */
export function operaToolDetail(data: ToolData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/rule/operaToolDetail',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 分页获取业务树结构
 * @param data 查询参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function getPlaceList(data: ModuleQuery, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/parcel/selectParcelList',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 根据指定id查询业务树
 * @param data 查询参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function getPlaceDetail(data: ModuleQuery, param?: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/parcel/selectParcel',
    method: 'post',
    data: data,
    params: param,
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}

/**
 * 根据ids查询地块数据
 * @param data 查询参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function getPrarcels(data: ModuleQuery, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/parcel/selectParcels',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 保存添加的字段组--权属人类型groupScope：1 当groupScope：2 在采集信息的权属人的添加 3 设置界址点和界址线的数据
 * @param data 字段组数据
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function saveFieldGroup(data: FieldGroupData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/field/operaFieldGroup',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 根据moduleId查询添加的权属人字段
 * @param data 查询参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function getOwnerListByModuleId(data: ModuleQuery, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/field/selectGroupAndDetail',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 根据组id查询组下面的字段信息
 * @param data 查询参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function getFieldListByGroupId(data: ModuleQuery, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/field/selectFieldListByGroup',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 保存权属信息的字段内容
 * @param data 字段数据
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function saveFieldInOwner(data: FieldGroupData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/field/operaField',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 通过字段id查询字段信息数据
 * @param data 查询参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function selectFieldById(data: ModuleQuery, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/field/selectFieldById',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 导出设置
 * @param params 导出设置数据
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function saveExportSetting(params: ExportData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/export/save',
    method: 'post',
    data: params,
    params: param
  });
}

/**
 * 导出设置列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function exportSettingList(params: ModuleQuery): AxiosPromise<any> {
  return request({
    url: 'qjt/export/list',
    method: 'post',
    params: params
  });
}

/**
 * 获取导出设置详情
 * @param data 设置ID
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function getExportDetail(data: string | number, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/export/id?Id=' + data,
    method: 'post',
    params: param
  });
}

/**
 * 导出
 * @param params 导出参数
 * @returns {AxiosPromise}
 */
export function downLoadPublic(params: ExportData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `/qjt/export`,
    method: 'post',
    data: params,
    params: param
  });
}

/**
 * 检测导出
 * @param data 导出参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function downLoadPublicCheck(data: ExportData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `/qjt/export/check`,
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 获取导出模板
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function getTempList(params: ModuleQuery): AxiosPromise<any> {
  return request({
    url: `/qjt/output/template/get`,
    method: 'get',
    params: params
  });
}

/**
 * 上传模板
 * @param params 上传数据
 * @param moduleId 模块ID
 * @param companyId 公司ID
 * @returns {AxiosPromise}
 */
export function uploadTemplate(params: FormData, moduleId: string | number, companyId?: string | number): AxiosPromise<any> {
  let url = `/qjt/output/template/upload/${moduleId}`;
  if (companyId && companyId != '' && companyId != undefined && companyId != null) {
    url = `/qjt/output/template/upload/${moduleId}?companyId=${companyId}`;
  }
  return request({
    url: url,
    method: 'post',
    data: params
  });
}

/**
 * 下载项目
 * @param oname 文件名
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function downLoadTemplate(oname: string, param: ModuleInfo): AxiosPromise<Blob> {
  return request({
    url: `/qjt/file/downloadone/${oname}`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 删除模板
 * @param data 删除参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function delTemplate(data: ModuleData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `/qjt/output/template/delete`,
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 拷贝模块
 * @param moudleId 模块ID
 * @returns {AxiosPromise}
 */
export function copyModal(moudleId: string | number): AxiosPromise<any> {
  return request({
    url: `/qjt/rule/copy?id=${moudleId}`,
    method: 'post'
  });
}

/**
 * 粘贴模块
 * @param params 粘贴参数
 * @returns {AxiosPromise}
 */
export function pasteModal(params: ModuleData): AxiosPromise<any> {
  let url = `/qjt/rule/paste?code=${params.code}&name=${params.name}`;
  if (params.companyId && params.companyId != undefined && params.companyId != '') {
    url = `/qjt/rule/paste?code=${params.code}&name=${params.name}&companyId=${params.companyId}`;
  }
  return request({
    url: url,
    method: 'post'
  });
}

/**
 * 图层步骤数据内容
 * @param data 步骤数据
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function saveStep(data: ModuleData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `/qjt/step/save`,
    method: 'post',
    data: data,
    param: param
  });
}

/**
 * 查询图层步骤
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function selectStep(params: ModuleQuery): AxiosPromise<any> {
  return request({
    url: `/qjt/step/select`,
    method: 'post',
    params: params
  });
}

/**
 * 查看指定模块
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function selectModuleById(params: ModuleQuery): AxiosPromise<any> {
  return request({
    url: `/qjt/rule/selectModuleById`,
    method: 'post',
    params: params
  });
}

/**
 * 查询默认模块列表
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function selectDefalutList(param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `/qjt/rule/selectModuleDefaultList`,
    method: 'post',
    params: param
  });
}

/**
 * 将默认模块作为自己的模块一公司的模块添加到自己公司
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function addDefaultModule(params: ModuleQuery): AxiosPromise<any> {
  return request({
    url: `qjt/rule/addDefaultModule`,
    method: 'post',
    params: params
  });
}

/**
 * 根据多个地块ID查询地固定属性组的字段值
 * @param fieldName 字段名
 * @param linkId 链接ID
 * @param parcels 地块数组
 * @param param 接口中在请求路径后面拼接 moudleId
 * // TODO 这个参数过多，可能需要修改
 * @returns {AxiosPromise}
 */
export function selectFields(fieldName: string, linkId: string | number, parcels: any[], param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `qjt/parcel/selectFields?fieldName=${fieldName}&linkId=${linkId}`,
    method: 'post',
    data: parcels,
    param: param
  });
}

/**
 * 新增检查规则
 * @param data 规则数据
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function addExamine(data: RuleData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `qjt/ruleCheck/save`,
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 拓扑检查规则列表
 * @param params 查询参数
 * @returns {AxiosPromise}
 */
export function examineList(params: ModuleQuery): AxiosPromise<any> {
  return request({
    url: `qjt/ruleCheck/select`,
    method: 'post',
    params: params
  });
}

/**
 * 表达式排序
 * @param data 排序数据
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function formulaOrder(data: ModuleData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `qjt/formula/formulaOrder`,
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 根据表达式计算值并更新
 * @param data 更新参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function updateParcel(data: ModuleData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `qjt/simple/updateParcel`,
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 根据表达式计算值并更新
 * @param data 更新参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function updateParcelNew(data: ModuleData, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: `qjt/simple/updateParcelNew`,
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 导出
 * @param data 导出参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function updateParcelFromMain(list: any, moduleId: number): AxiosPromise<any> {
  return request({
    url: `qjt/simple/updateParcelFromMain?moduleId=${moduleId}`,
    method: 'post',
    data: list
  });
}

/**
 * 通过节点名称查询多个节点id
 * @param data 查询参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function selectRulesMore(data: ModuleQuery, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: '/qjt/rule/selectRulesMore',
    method: 'post',
    data: data,
    params: param
  });
}

/**
 * 树结构查询数据
 * @param data 查询参数
 * @param param 接口中在请求路径后面拼接 moudleId
 * @returns {AxiosPromise}
 */
export function selectParcelFromTree(data: ModuleQuery, param: ModuleInfo): AxiosPromise<any> {
  return request({
    url: 'qjt/parcel/selectParcelFromTree',
    method: 'post',
    data: data,
    params: param,
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果
          return JSON.parse(JSON.stringify(JSONbig.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return {
            data
          };
        }
      }
    ]
  });
}
