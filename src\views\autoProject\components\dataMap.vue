<template>
  <div v-loading.fullscreen.lock="fullscreenLoading" class="gisMap-main" id="layerMapRef">
    <div id="viewDiv" ref="gisMapRef" class="map"></div>
    <div class="right-handle-div">
      <div class="min-handle-item" style="margin-bottom: 12px" @click="showTL = true" v-if="route.query.iscq">
        <svg-icon icon-class="tuli" />
      </div>
      <div class="min-handle-item" @click="zoomMap">
        <svg-icon icon-class="zoom" />
      </div>
      <!-- 图层 -->
      <div class="min-handle-item handle-marign" @click="showChangeTC = !showChangeTC" v-show="userStore.vipType == 3">
        <svg-icon icon-class="tuceng" />
      </div>
      <div class="min-handle-item handle-marign" @click="showChangeMap = !showChangeMap" :class="{ 'handle-marign-active': showChangeMap }">
        <svg-icon icon-class="map" />
      </div>
      <div class="tuceng-box" v-show="showChangeTC">
        <div style="margin-left: 8px">
          <span>总数:{{ treeCount }}</span>
          <span style="padding-left: 8px">勾选:{{ defaultCheckList.length }}</span>
        </div>
        <el-tree
          ref="treeRef"
          :data="layerList"
          show-checkbox
          node-key="id"
          @node-click="handleNodeClick"
          @check-change="handleNodeChange"
          :default-checked-keys="defaultCheckList"
          :props="defaultProps"
          :check-strictly="true"
          :default-expand-all="true"
        >
        </el-tree>
      </div>
    </div>
    <div class="map-change" v-show="showChangeMap">
      <div class="map-item map-left" :class="{ 'map-active': checkedMap == 'normal' }" @click="changeMap(1)">
        <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'normal' }">天地图地图矢量</div>
      </div>
      <div class="map-item map-right" :class="{ 'map-active': checkedMap == 'image' }" @click="changeMap(2)">
        <div class="map-footer" :class="{ 'map-footer-active': checkedMap == 'image' }">天地图影像</div>
      </div>
    </div>
    <div class="map-copyRight">
      <img src="https://api.tianditu.gov.cn/v4.0/image/logo.png" alt="" class="copy-ico" />
      GS（2024）0568号 - 甲测资字1100471
    </div>
    <div class="attr-box" v-show="showAttr" :style="{ 'top': top, 'left': left }">
      <div class="attr-div">
        <div class="attr-close" @click="showAttr = false"><i class="el-icon-circle-close"></i></div>
        <div class="attr-row title">属性</div>
        <div class="attr-row" v-for="(item, index) in properties" :key="index" :class="{ 'two': index % 2 != 0 }">
          <div class="left">{{ item.label }}</div>
          <div class="right">{{ item.value }}</div>
        </div>
      </div>
    </div>
    <!-- 查看数据大屏 -->
    <div class="check-screen" @click="jumpScreen" v-show="userStore.vipType == 3">数据大屏</div>
    <!-- 左上方操作框 -->
    <div class="handle-top">
      <el-tooltip class="item" effect="dark" content="展开操作栏" placement="top">
        <svg-icon icon-class="pick_on" title="展开操作栏" class="pick-up" v-show="!isHandleOn" @click="isHandleOn = true" />
      </el-tooltip>
      <template v-if="isHandleOn">
        <div class="handle-item" @click="addProject">新建</div>
        <div class="hr"></div>
        <el-tooltip class="item" effect="dark" content="收起操作栏" placement="top">
          <svg-icon icon-class="pick_off" class="pick-up" @click="isHandleOn = false" />
        </el-tooltip>
      </template>
    </div>
    <!-- 拆迁进度展示数据 -->
    <div class="cqplan-div" :style="{ top: cqplantop + 'px', left: cqplanleft + 'px' }" v-show="showCQPlan">
      <div class="cqplan-title">{{ parcelNameZ }}</div>
      <div class="hr"></div>
      <div class="cqplan-content">
        <!-- 已拆除 -->
        <div class="item cqOk">已拆除{{ msgMsg.cqOkNum }}</div>
        <!-- 未拆除 -->
        <div class="item cqON" style="border-left: none">未拆除{{ msgMsg.cqNoNum }}</div>
      </div>
    </div>
    <!-- 图例 拆迁户专用 -->
    <div class="right-map-color" v-show="showTL && route.query.iscq">
      <div class="title">
        <div>图例</div>
        <div class="right-close" @click="closeTL">×</div>
      </div>
      <div class="hr"></div>
      <div class="tuli-content">
        <div class="item">
          已完成调查
          <div class="color-box color1"></div>
        </div>
        <div class="item">
          已完成测绘
          <div class="color-box color2"></div>
        </div>
        <div class="item">
          已完成确权
          <div class="color-box color3"></div>
        </div>
        <div class="item">
          已完成评估
          <div class="color-box color4"></div>
        </div>
        <div class="item">
          已完成签约
          <div class="color-box color5"></div>
        </div>
        <div class="item">
          已完成腾空
          <div class="color-box color6"></div>
        </div>
        <div class="item">
          已完成拆除
          <div class="color-box color7"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getToken } from '@/utils/auth';
import { loadModules } from 'esri-loader';
// import autoImage from '@/components/autoImage/index.vue';
import authImg from '@/components/authImg/index.vue';
import { getPlaceDetail, selectModuleById as selectModuleByIdApi } from '@/api/modal';
import { hexToRgba } from '@/utils/validate';
import { getTCList, getShpTree as getShpTreeApi } from '@/api/issueManager';
import axios from 'axios';
import { getScreenList, saveScreen } from '@/api/dataScreen';
import taskDefaultBigData from '@/data/taskDefaultBigData.json';
import { throttle } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { useRoute } from 'vue-router';
const route = useRoute();
const userStore = useUserStore();
import { useProjectStore } from '@/store/modules/project';
const projectStore = useProjectStore();
import { useRouter } from 'vue-router';
const router = useRouter();

const config = {
  css: import.meta.env.VITE_APP_ARCGIS_CONFIGCSS,
  url: import.meta.env.VITE_APP_ARCGIS_CONFIGJS
};
import { TDT_BASE_URL, TDT_TOKEN } from '@/constants';
const tiandituBaseUrl = TDT_BASE_URL; //天地图服务地址
const mapToken = TDT_TOKEN; //天地图管网申请token
let fontLayer = null; //文字layer和点
let graphicsLayerAll = null; //所有宗地layer
let childGraphics = null; //查询详情 子要素图形layer
let degGraphics = null; //方位角layer
let labelLayer: any = null; //标注层
let jzdzbLayer = null; //界址点坐标 高亮用
let tiledLayer = null; //影像
let tiledLayerAnno = null; //影像标记
let normalLayer = null; //矢量底图
let normalAnno = null; //矢量标记
const tolerance = 3; //偏差值
const sketch = null;
const handleExtentChange = throttle(() => {
  // 处理地图范围变化的逻辑
}, 200); // 每 200 毫秒处理一次

// ---Props---
interface Props {
  parcelList: Array<any>;
  nowCheckedZD?: number;
  changeParcelList: boolean;
  defaultWMS: string;
}

const props = withDefaults(defineProps<Props>(), {
  parcelList: () => [],
  nowCheckedZD: 0,
  changeParcelList: true,
  defaultWMS: ''
});

//  ---定义emit---
import { defineEmits } from 'vue';
const emit = defineEmits<{
  (e: 'checkedOneParcelId', parcelId: any): void;
  (e: 'getLinyeData'): void;
  (e: 'changeNowCheckedZD', id: any): void;
  (e: 'noInitCenter'): void;
}>();

// --- 定义变量部分 ---
const currentSelectZd: any = ref({}); //选中的宗地
const baseUrlImg = ref(import.meta.env.VITE_APP_BASE_API + '/system/user/profile/downloadone/');
let map = null; //地图容器
let view = null; //mapview
const allZD: any = ref([]); //选中项目所有宗地
const fit = ref('cover'); //地图缩放方式
const oldHighlight: any = ref(null); //之前高亮的图形
const fullscreenLoading = ref(false);
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API);
const token = ref(getToken());
const showChangeMap = ref(false);
const checkedMap = ref('image'); //显示的底图 默认影像
const oldSpeHightGrc = ref(null); //上一个高亮的方位角要素
const showChangeTC = ref(false); //显示选择图层
const layerList = ref<Array<any>>([]); //图层列表
const checkList = ref([]); //选中的图层
const base = ref(import.meta.env.VITE_APP_LAYER_BASE);
const properties = ref([]); //点击的要素数据
const showAttr = ref(false); //是否展示要素属性
const top = ref('0px');
const left = ref('0px');
const isHandleOn = ref(true); //顶部操作展开
const showCQPlan = ref(false); //是否显示拆迁进度
const msgMsg: any = reactive({}); //拆迁进度显示的信息
const nowPoint = ref(null); //拆迁进度当前高亮的图形的中间点
const cqplanleft = ref(0); // 拆迁进度显示弹窗左侧距离
const cqplantop = ref(0); //拆迁进度显示弹窗上侧距离
const showTL = ref(false); //是否显示图例
const parcelNameZ = ref(''); //栋名字 拆迁进度需要
const reloadFirst = ref(true); //是否是重新绘制第一次
const defaultProps = reactive({
  children: 'children',
  label: 'label',
  id: 'id'
}); // 属性结构绑定的节点
// 当前树结构的层级展示
const treeCount = ref(0); // 当前树结构的层级展示
const defaultCheckList = ref([]); // 默认勾选当前树节点
const mapDescVisible = ref(false); // 设置图层描述
const mapDescContent = ref(''); // 图层描述的内容
const treeRef = ref(null);
const gisMapRef = ref(null);
const msgList = ref([]); // 拆迁进度信息
const nowLayerList = ref<any[]>([]); // 当前选中的图层

// --- watch ---
watch(
  () => props.nowCheckedZD,
  (val: any, oldVal: any) => {
    getPlaceDetail(val).then((res) => {
      if (res.code == 200) {
        currentSelectZd.value = res.data;
        highlightZD();
        //选中某个宗地 父要素
        //1、先清除childGraphics 2、再绘制所有子要素
        childGraphics.removeAll();
        // 清除所有方位角
        clearDeg();
        // 迭代绘制子要素
        drawChildEle(res.data.list);
      } else {
        ElMessage.error(res.msg);
      }
    });
  },
  { deep: true }
);

// --- 定义方法部分 ---

/**
 * 当前选中的数据字段内容
 */
const handleNodeClick = (data) => {
  const index = data.mapName.indexOf(':');
  const title = data.mapName.substring(index + 1, data.mapName.length);
  // 得到title后 找到相应的图层 居中
  const layer = map.findLayerById(title);
  view.center = [layer.fullExtent.center.longitude, layer.fullExtent.center.latitude];
};

/**
 * 选择图层
 * @param data 选择的图层
 * @param flg 当前选中图层的状态

 */
const handleNodeChange = (data, flg) => {
  // 存在的时候 加载wmslayer
  if (flg) {
    const index = data.mapName.indexOf(':');
    const title = data.mapName.substring(index + 1, data.mapName.length);
    const url = `${base.value}${data.mapName.substring(0, index)}/${title}/wms`;
    initWMS(url, title);
  } else {
    //取消选中的时候要把对应的wmslayer移除
    const index = data.mapName.indexOf(':');
    const title = data.mapName.substring(index + 1, data.mapName.length);
    removeWMS(title);
  }
};

/**
 * 移除wmsLayer
 * @param title 图层名称
 */
const removeWMS = (title: any) => {
  const layer = map.findLayerById(title);
  if (layer) {
    map.remove(layer);
  }
};

/**
 * 查看数据图层结果树
 * @param data 图层数据
 */
const handleMapDetial = (data) => {
  mapDescVisible.value = true;
  if (data.descripition && data.descripition !== '' && data.descripition !== null) {
    mapDescContent.value = data.descripition;
  } else {
    mapDescContent.value = '暂无图层描述';
  }
};

/**
 * 测试展示数据
 */
const getShpTree = (flg) => {
  // 图层树结构
  getShpTreeApi().then((res) => {
    if (res.code == 200) {
      layerList.value = res.data;
      treeCount.value = countTotalNodes(res.data);
      // 默认勾选第一个图层
      nowLayerList.value = [];
      if (layerList.value.length > 0) {
        defaultCheckList.value.push(layerList.value[0].id);
        nowLayerList.value.push(layerList.value[0].mapName);
        changeLayer([layerList.value[0].mapName]);
      }
      layerList.value.length > 0 && changeLayer([layerList.value[0]?.mapName]);

      // 如果是图层要素 需要把要素绑定的图层默认选中
      if (props.defaultWMS) {
        for (let index = 0; index < layerList.value.length; index++) {
          if (layerList.value[index].layerName == props.defaultWMS) {
            layerList.value[index].checked = true;
            if (!checkList.value.includes(props.defaultWMS)) {
              checkList.value.push(layerList.value[index].layerName);
            }
            break;
          }
        }
      }
      if (!flg) {
        showChangeTC.value = !showChangeTC.value;
      }
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 获取当前的展示数据
 * @param nodes 当前层级的节点
 * @returns 当前层级的节点数
 */
const countTotalNodes = (nodes) => {
  let count = nodes.length; // 当前层级的节点数
  nodes.forEach((node) => {
    if (node.children && node.children.length > 0) {
      count += countTotalNodes(node.children); // 递归计算子节点数量并累加
    }
  });
  return count;
};

/**
 * 点击时高亮当前的宗地
 * @param item 当前宗地
 */
const getCurrentParcel = (item) => {
  currentSelectZd.value = item;
  highlightZD();
};

/**
 * 初始化林业
 * @param val 当前宗地
 * @param isInitCenter 是否初始化中心
 */
const initLinye = (val, isInitCenter) => {
  if (fontLayer) {
    fontLayer.removeAll();
  }
  if (childGraphics) {
    childGraphics.removeAll();
  }
  allZD.value = val;
  //加载所有宗地
  drawZD(isInitCenter);
};

/**
 * 初始化地图
 */
const init = () => {
  loadModules(
    [
      'esri/Map',
      'esri/views/MapView',
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/layers/WebTileLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/widgets/Compass',
      'esri/widgets/Zoom',
      'esri/widgets/ScaleBar',
      'esri/geometry/geometryEngine'
    ],
    config
  ).then(([Map, MapView, Graphic, GraphicsLayer, WebTileLayer, SpatialReference, Point, Compass, Zoom, ScaleBar, geometryEngine]) => {
    //球面墨卡托投影矢量底图
    tiledLayer = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=img_w/wmts&x={col}&y={row}&l={level}&tk=' + mapToken,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      spatialReference: new SpatialReference({
        wkid: 102100
      }),
      getTileUrl: function (level, row, col) {
        if (level <= 18) {
          // return `http://t0.tianditu.com/DataServer?T=img_w&x=${col}&y=${row}&l=${level}&tk=${mapToken}`
          return 'http://t' + (col % 8) + '.tianditu.gov.cn/DataServer?T=img_w/wmts&x=' + col + '&y=' + row + '&l=' + level + '&tk=' + mapToken;
        } else {
          return '';
          // return "http://t" +(207748 % 8) +".tianditu.com/DataServer?T=img_w/wmts&x="+ col +"&y=" +110397 +"&l="+18+"&tk=" + mapToken
        }
      }
    });

    //矢量标注(球面墨卡托投影)
    tiledLayerAnno = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=cia_w?T=vec_c/wmts&x={col}&y={row}&l={level}&tk=' + mapToken,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      spatialReference: new SpatialReference({
        wkid: 102100
      }),
      getTileUrl: function (level, row, col) {
        if (level <= 18) {
          return (
            'http://t' + (col % 8) + '.tianditu.gov.cn/DataServer?T=cia_w?T=vec_c/wmts&x=' + col + '&y=' + row + '&l=' + level + '&tk=' + mapToken
          );
        } else {
          return '';
        }
      }
    });

    //经纬度投影 矢量底图
    normalLayer = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=vec_w/wmts&x={col}&y={row}&l={level}&tk=' + mapToken,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false,
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });
    //矢量注记
    normalAnno = new WebTileLayer({
      urlTemplate: tiandituBaseUrl + '/DataServer?T=cva_w/wmts&x={col}&y={row}&l={level}&tk=' + mapToken,
      subDomains: ['t0', 't1', 't2', 't3', 't4', 't5', 't6', 't7'],
      visible: false,
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });
    map = new Map({
      // topo-vector
      // basemap: "satellite",
      basemap: {
        baseLayers: [tiledLayer, tiledLayerAnno, normalLayer, normalAnno]
      },
      // ground: "world-elevation",
      logo: false,
      spatialReference: {
        wkid: 102100
      },
      renderer: {
        type: 'webgl'
      }
    });
    view = new MapView({
      //这个是让地图显示在id为map_contentView的标签上，是id叫他不是其他的
      container: gisMapRef.value,
      map: map,
      center: [116.39126, 39.90763],
      zoom: 6,
      ui: {
        // components: ["zoom", "compass"]
        components: []
      },
      spatialReference: new SpatialReference({
        wkid: 102100
      })
    });

    //设置最大缩放等级
    view.constraints = {
      minZoom: 2,
      maxZoom: 24
    };

    const compassWidget = new Compass({ view: view });
    view.ui.add(compassWidget, 'bottom-right');
    const zoom = new Zoom({
      view: view
    });
    view.ui.add(zoom, 'bottom-right');
    view.ui.remove('attribution');
    const scaleBar = new ScaleBar({
      view: view,
      unit: 'metric',
      style: 'line'
    });
    // Add widget to the bottom left corner of the view
    view.ui.add(scaleBar, {
      position: 'bottom-right'
    });
    const that = this;
    //添加文字标注layer到map
    fontLayer = new GraphicsLayer({
      id: '234',
      visible: false
    });
    map.add(fontLayer, 9999);
    //添加文字标注layer到map
    labelLayer = new GraphicsLayer({
      id: 'labelLayer',
      minScale: 60000
    });
    map.add(labelLayer, 9999);
    //添加宗地图形layer到map
    graphicsLayerAll = new GraphicsLayer({
      id: '123',
      minScale: 200000
    });
    map.add(graphicsLayerAll);
    //子要素layer
    childGraphics = new GraphicsLayer({
      id: 'childEle',
      minScale: 200000
    });
    map.add(childGraphics);
    degGraphics = new GraphicsLayer({
      id: 'degEle'
    });
    map.add(degGraphics);
    jzdzbLayer = new GraphicsLayer({
      id: 'jzd'
    });
    map.add(jzdzbLayer);
    // 这里需要区分一下 如果是地址有parcelId的就单独请求这一条数据
    if (route.query.parcelId) {
      emit('checkedOneParcelId', route.query.parcelId);
    } else {
      //地图初始化完成后再调用父组件获取所有林业信息  重要
      emit('getLinyeData'); //true 表示第一次请求
      getShpTree(true);
    }
    // 处理节流防抖
    view.watch('extent', handleExtentChange);
    view.on('click', function (event) {
      view.hitTest(event).then((res) => {
        if (res.results.length != 0 && !props.defaultWMS) {
          const id = res.results[0].graphic.id;
          emit('changeNowCheckedZD', id);
        } else {
          //尝试获取图层信息
          executeIdentify(event);
        }
      });
    });
    view.watch('extent', function (evt) {
      if (showCQPlan.value) {
        //代表有拆迁进度信息
        updateCQPlan();
      }
    });
  });
};

/**
 * 更新拆迁进度浮窗
 */
const updateCQPlan = () => {
  const screenPoint = view.toScreen(nowPoint.value);
  cqplanleft.value = screenPoint.x - 60;
  cqplantop.value = screenPoint.y - 40;
};

/**
 * 清除拆迁进度浮窗
 */
const clearCQLabel = () => {
  showCQPlan.value = false;
  msgList.value = [];
};
/**
 * 取消高亮 如果没有图形的时候
 */
const initLableCQ = () => {
  if (oldHighlight.value) {
    oldHighlight.value.remove();
  }
  // 如果有拆迁房标注 需要同时去掉
  showCQPlan.value = false;
  msgList.value = [];
};

/**
 * 绘制宗地
 * @param isInitCenter 是否初始化中心
 */
const drawZD = (isInitCenter) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    graphicsLayerAll.removeAll();
    let oldIndex = -1;
    if (props.nowCheckedZD) {
      // 当前要素列表是否存在之前选中的id
      oldIndex = allZD.value.findIndex((item) => item.id == props.nowCheckedZD);
    }
    allZD.value.forEach((v, idx) => {
      if (v.geomArcgis) {
        let bgColor = null;
        if (v.styleAttribution.polylineColor.includes('rgba')) {
          bgColor = v.styleAttribution.polylineColor;
        } else {
          bgColor = hexToRgba(v.styleAttribution.polylineColor, '0.2');
          if (v.styleAttribution.polygonFillColor) {
            bgColor = hexToRgba(v.styleAttribution.polygonFillColor);
          }
        }
        const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
        if (v.graphicalType == 1) {
          //点
          //创建文本符号
          let textSymbol = new TextSymbol();

          textSymbol = {
            type: 'simple-marker', // autocasts as SimpleLineSymbol()
            color: v.styleAttribution.pointColor,
            width: v.styleAttribution.pointSize || 2
          };
          const graphic = new Graphic({
            geometry: polygon,
            symbol: textSymbol,
            id: v.id
          });
          if (idx == 0) {
            //定位第一个
            view.center = [polygon.longitude, polygon.latitude];
            view.zoom = 18;
          }
          graphicsLayerAll.add(graphic);
        } else if (v.graphicalType == 2) {
          //线
          const lineSymbol = {
            type: 'simple-line', // autocasts as new SimpleLineSymbol()
            color: v.styleAttribution.polylineColor, // RGB color values as an array
            width: v.styleAttribution.polylineWidth || 1,
            style: v.styleAttribution.polylineType
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: lineSymbol,
            id: v.id
          });
          if (idx == 0) {
            //定位第一个
            view.center = [polygon.extent.center.longitude, polygon.extent.center.latitude];
            view.zoom = 18;
          }
          graphicsLayerAll.add(polygonGraphic);
        } else if (v.graphicalType == 3) {
          //面
          const simpleFillSymbol = {
            type: 'simple-fill',
            outline: {
              color: v.styleAttribution.polylineColor,
              width: v.styleAttribution.polylineWidth || 1,
              style: v.styleAttribution.polylineType || 'solid'
            }
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: simpleFillSymbol,
            id: v.id
          });
          if (props.nowCheckedZD && oldIndex != -1) {
            //选中了数据，定位到数据的位置 并且在当前绘制的宗地存在选中的宗地id
            if (v.id == props.nowCheckedZD) {
              view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
              view.zoom = 18;
            }
          } else {
            //代表没有选中数据 默认定位第一个
            if (idx == 0 && isInitCenter) {
              view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
              view.zoom = 18;
              emit('noInitCenter');
            }
          }
          graphicsLayerAll.add(polygonGraphic);
        }
      }
    });
  });
};
/**
 * 高亮当前选中宗地
 */
const highlightZD = () => {
  loadModules(
    [
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/geometry/projection',
      'esri/symbols/TextSymbol'
    ],
    config
  ).then(([Graphic, GraphicsLayer, SpatialReference, Point, projection, TextSymbol]) => {
    //先清除之前的文字标注
    fontLayer.removeAll();
    //找到所有宗地图形的layer
    const layer = map.findLayerById('123');
    layer.graphics.items.forEach((v) => {
      if (v.id == props.nowCheckedZD) {
        getzhijieList(v.geometry);
        //给中心点为当前选中宗地
        view.center = [v.geometry.centroid.longitude, v.geometry.centroid.latitude];
        //如果地图缩放等级小于18强行设置为18
        if (view.zoom < 18) {
          view.zoom = 18;
        }
        view.whenLayerView(v.layer).then(function (layerView) {
          oldHighlight.value = layerView.highlight(v);
        });
        if (oldHighlight.value) {
          oldHighlight.value.remove(); //取消上一个高亮宗地
        }
      }
    });
  });
};

/**
 * 高亮图形的界址点坐标显示 自己用
 */
const hightJZD = (geometry) => {
  jzdzbLayer.removeAll();
  loadModules(
    [
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/geometry/projection',
      'esri/symbols/TextSymbol',
      'esri/geometry/geometryEngine',
      'esri/geometry/Polygon'
    ],
    config
  ).then(([Graphic, GraphicsLayer, SpatialReference, Point, projection, TextSymbol, geometryEngine, Polygon]) => {
    geometry.rings.forEach((v) => {
      v.forEach((k) => {
        //创建文本符号
        let textSymbol = new TextSymbol();
        //文本的内容的实在
        textSymbol = {
          type: 'text',
          text: `X:${k[0]},Y:${k[1]}`,
          color: [255, 0, 0],
          haloSize: '1px',
          xoffset: 0,
          yoffset: 0
        };
        const point = new Point(k[0], k[1], new SpatialReference({ wkid: 102100 }));
        const graphic = new Graphic({ geometry: point, symbol: textSymbol });
        jzdzbLayer.add(graphic);
      });
    });
  });
};

/**
 * 组合界址点位置 按照J1在最左上方为准
 */
const getzhijieList = (geometry) => {
  loadModules(
    [
      'esri/Graphic',
      'esri/layers/GraphicsLayer',
      'esri/geometry/SpatialReference',
      'esri/geometry/Point',
      'esri/geometry/projection',
      'esri/symbols/TextSymbol',
      'esri/geometry/geometryEngine',
      'esri/geometry/Polygon'
    ],
    config
  ).then(([Graphic, GraphicsLayer, SpatialReference, Point, projection, TextSymbol, geometryEngine, Polygon]) => {
    if (geometry.rings) {
      let beginNum = 1; //起始为J1 用于处理孤岛
      geometry.rings.forEach((v, vdx) => {
        const polygon = new Polygon({
          type: 'polygon',
          rings: v,
          spatialReference: geometry.spatialReference
        });
        const ymax = polygon.extent.ymax;
        const xmin = polygon.extent.xmin;
        polygon.rings[0].pop();
        const area = geometryEngine.geodesicArea(polygon, 'square-meters');
        // 如果得到面积是负数为反着画，如果是正数是正着画
        const distanceList = [];
        polygon.rings[0].forEach((v) => {
          const distance = Math.sqrt(Math.pow(Math.abs(xmin - v[0]), 2) + Math.pow(Math.abs(ymax - v[1]), 2));
          distanceList.push(distance);
        });
        const min = Math.min(...distanceList);
        const index = distanceList.indexOf(min);
        let endList = []; //最终的数组
        if (index == 0) {
          //即第一个点就是J1，不用分割数组
          if (area > 0) {
            //顺时针
            endList = polygon.rings[0];
          } else {
            //逆时针
            endList.push(polygon.rings[0][0]);
            const tem = JSON.parse(JSON.stringify(polygon.rings[0])).reverse();
            tem.forEach((v, idx) => {
              if (idx != tem.length - 1) {
                endList.push(v);
              }
            });
          }
        } else if (index == distanceList.length - 1) {
          //最后一个点是J1，也不用分割，直接倒序
          if (area > 0) {
            //顺时针
            endList.push(polygon.rings[0][index]);
            polygon.rings[0].forEach((v, idx) => {
              if (idx != index) {
                endList.push(v);
              }
            });
          } else {
            //逆时针
            endList = JSON.parse(JSON.stringify(polygon.rings[0])).reverse();
          }
        } else {
          //需要分割数组
          if (area > 0) {
            //顺时针
            const left = [];
            const right = [];
            polygon.rings[0].forEach((v, idx) => {
              if (idx < index) {
                //左侧
                left.push(v);
              } else {
                right.push(v);
              }
            });
            endList = right.concat(left);
          } else {
            //逆时针
            let left = [];
            let right = [];
            polygon.rings[0].forEach((v, idx) => {
              if (idx <= index) {
                //左侧
                left.push(v);
              } else {
                right.push(v);
              }
            });
            left = left.reverse();
            right = right.reverse();
            endList = left.concat(right);
          }
        }
        //创建文字标注
        endList.forEach((k, kdx) => {
          //创建文本符号
          let textSymbol = new TextSymbol();
          //文本的内容的实在
          textSymbol = {
            type: 'text',
            text: `${beginNum}`,
            // text: `X:${k[0]},Y:${k[1]}`,
            color: [255, 0, 0],
            haloSize: '1px',
            xoffset: 0,
            yoffset: 0
          };
          const point = new Point(k[0], k[1], new SpatialReference({ wkid: 102100 }));
          const graphic = new Graphic({ geometry: point, symbol: textSymbol });
          beginNum++;
          fontLayer.add(graphic);
        });
      });
    }
  });
};

/**
 * F11效果
 */
const zoomMap = () => {
  document.documentElement['webkitRequestFullscreen']();
};

/**
 * 切换底图
 */
const changeMap = (type) => {
  if (type == 1) {
    //地图
    checkedMap.value = 'normal';
    tiledLayer.visible = false;
    tiledLayerAnno.visible = false;
    normalLayer.visible = true;
    normalAnno.visible = true;
  } else if (type == 2) {
    //影像图
    checkedMap.value = 'image';
    tiledLayer.visible = true;
    tiledLayerAnno.visible = true;
    normalLayer.visible = false;
    normalAnno.visible = false;
  }
};

/**
 * 迭代绘制所有有图形的子要素
 */
const drawChildEle = (list) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    list.forEach((v, idx) => {
      if (v.geomArcgis) {
        let bgColor = hexToRgba(v.styleAttribution.polylineColor, '0.2');
        if (v.styleAttribution.polygonFillColor) {
          bgColor = hexToRgba(v.styleAttribution.polygonFillColor);
        }
        const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
        let color = v.styleAttribution.polylineColor;
        if (polygon.type == 'polygon') {
          //面
          // 面 并且是第五级levelNum=5 拆迁户专用颜色区分
          if (route.query.iscq && v.levelNum == 5) {
            // 判断拆迁进度
            for (let i = 0; i < v.fieldInstanceModels.length; i++) {
              if (v.fieldInstanceModels[i].groupName == '征迁进度') {
                switch (v.fieldInstanceModels[i].attribution.ZQJD) {
                  case '已完成调查':
                    bgColor = hexToRgba('#0199FF', '0.8');
                    color = '#0199FF';
                    break;
                  case '已完成确权':
                    bgColor = hexToRgba('#9B66FD', '0.8');
                    color = '#9B66FD';
                    break;
                  case '已完成签约':
                    bgColor = hexToRgba('#00FF80', '0.8');
                    color = '#00FF80';
                    break;
                  case '已完成拆除':
                    bgColor = hexToRgba('#FF8D02', '0.8');
                    color = '#FF8D02';
                    break;
                  case '已完成测绘':
                    bgColor = hexToRgba('#0002CC', '0.8');
                    color = '#0002CC';
                    break;
                  case '已完成评估':
                    bgColor = hexToRgba('#FEFF02', '0.8');
                    color = '#FEFF02';
                    break;
                  case '已完成腾空':
                    bgColor = hexToRgba('#FF01FD', '0.8');
                    color = '#FF01FD';
                    break;
                  default:
                    break;
                }
                break;
              }
            }
          }
          const simpleFillSymbol = {
            type: 'simple-fill',
            // color: [240,230,140, 0.8],  // Orange, opacity 80%
            color: bgColor,
            outline: {
              // color: [255, 255, 255],
              color: color,
              width: v.styleAttribution.polylineWidth || 1,
              style: v.styleAttribution.polylineType || 'solid'
            }
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: simpleFillSymbol,
            id: v.id
          });
          childGraphics.add(polygonGraphic);
        } else if (polygon.type == 'polyline') {
          // 线
          const lineSymbol = {
            type: 'simple-line', // autocasts as new SimpleLineSymbol()
            color: v.styleAttribution.polylineColor, // RGB color values as an array
            width: v.styleAttribution.polylineWidth || 1,
            style: v.styleAttribution.polylineType || 'solid'
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: lineSymbol,
            id: v.id
          });
          childGraphics.add(polygonGraphic);
        } else if (polygon.type == 'point') {
          //点
          let pointColor = '#ff0000';
          if (v.styleAttribution.pointColor) {
            pointColor = v.styleAttribution.pointColor;
          }
          //创建文本符号
          let textSymbol = new TextSymbol();
          //文本的内容的实在
          textSymbol = {
            type: 'text',
            text: v.parcelName,
            color: hexToRgba(pointColor),
            haloSize: '1px',
            xoffset: 0,
            yoffset: 0
          };
          const graphic = new Graphic({
            geometry: polygon,
            symbol: textSymbol,
            id: v.id
          });
          childGraphics.add(graphic);
        }
      }
      if (v.list.length != 0) {
        drawChildEle(v.list);
      }
    });
  });
};

/**
 * 高亮某个图形
 */
const endChangHighLight = (data) => {
  let heightGraphics = null;
  if (data.levelNum == 1) {
    //代表是第一级 宗地
    for (let index = 0; index < graphicsLayerAll.graphics.items.length; index++) {
      if (graphicsLayerAll.graphics.items[index].id == data.id) {
        heightGraphics = graphicsLayerAll.graphics.items[index];
        break;
      }
    }
    getzhijieList(heightGraphics.geometry);
  } else {
    //代表是子要素
    for (let index = 0; index < childGraphics.graphics.items.length; index++) {
      if (childGraphics.graphics.items[index].id == data.id) {
        heightGraphics = childGraphics.graphics.items[index];
        break;
      }
    }
  }
  //如果地图缩放等级小于18强行设置为18
  if (view.zoom < 18) {
    view.zoom = 18;
  }
  if (heightGraphics.geometry.type == 'polygon') {
    if (heightGraphics.geometry.centroid) {
      //给中心点为当前选中宗地
      view.center = [heightGraphics.geometry.centroid.longitude, heightGraphics.geometry.centroid.latitude];
    }
  } else if (heightGraphics.geometry.type == 'polyline') {
    if (heightGraphics.geometry.extent.center) {
      //给中心点为当前选中宗地
      view.center = [heightGraphics.geometry.extent.center.longitude, heightGraphics.geometry.extent.center.latitude];
    }
  } else if (heightGraphics.geometry.type == 'point') {
    if (heightGraphics.geometry) {
      //给中心点为当前选中宗地
      view.center = [heightGraphics.geometry.longitude, heightGraphics.geometry.latitude];
    }
  }
  if (oldHighlight.value) {
    oldHighlight.value.remove(); //取消上一个高亮宗地
  }
  view.whenLayerView(heightGraphics.layer).then(function (layerView) {
    oldHighlight.value = layerView.highlight(heightGraphics);
  });
};

// 绘制方位角
const initDeg = (val, deg, src) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    const point = {
      type: 'point',
      longitude: val[0],
      latitude: val[1]
    };
    const fillSymbol = {
      type: 'picture-marker', //"picture-fill",
      url: import(`@/assets/images/deg.png`), //"https://static.arcgis.com/images/Symbols/Shapes/BlackStarLargeB.png",
      width: '20px',
      height: '53px',
      outline: {
        style: 'solid'
      },
      angle: deg
    };

    const pointGraphic = new Graphic({
      geometry: point,
      symbol: fillSymbol,
      id: src
    });
    degGraphics.add(pointGraphic);
  });
};

/**
 * 清除图片方位角
 */
const clearDeg = () => {
  degGraphics.removeAll();
};

/**
 * 方位角 放大有方位角图片时候 高亮地图对应的要素 通过地址
 * @param {*} url 地址
 */
const heightSpeGrc = (url) => {
  for (let index = 0; index < degGraphics.graphics.items.length; index++) {
    if (degGraphics.graphics.items[index].id == url) {
      view.whenLayerView(degGraphics.graphics.items[index].layer).then(function (layerView) {
        oldSpeHightGrc.value = layerView.highlight(degGraphics.graphics.items[index]);
        view.center = [degGraphics.graphics.items[index].geometry.longitude, degGraphics.graphics.items[index].geometry.latitude];
        if (view.zoom < 18) {
          view.zoom = 18;
        }
      });
      if (oldSpeHightGrc.value) {
        oldSpeHightGrc.value.remove(); //取消上一个高亮宗地
      }
      break;
    }
  }
};

/**
 * 清除高亮的特殊方位角
 */
const clearSpeHightGrc = () => {
  if (oldSpeHightGrc.value) {
    oldSpeHightGrc.value.remove(); //取消上一个高亮宗地
  }
};
/**
 * 显示图层
 * @param {*} flg flg == true 的时候是默认请求图层列表 给图层要素赋值选中的 不需要显示图层信息 避免当是图层要素的时候没有点击图层就直接点击地图查看属性
 */
const showTc = (flg) => {
  if (layerList.value.length == 0) {
    //没有图层的时候请求图层
    getTCList().then((res) => {
      if (res.code == 200) {
        res.data.forEach((v) => {
          const index = v.mapName.indexOf(':');
          const title = v.mapName.substring(index + 1, v.length);
          const ite = {
            layerName: v,
            title: title
          };
          layerList.value.push(ite);
        });

        // 如果是图层要素 需要把要素绑定的图层默认选中
        if (props.defaultWMS) {
          for (let index = 0; index < layerList.value.length; index++) {
            if (layerList.value[index].layerName == props.defaultWMS) {
              layerList.value[index].checked = true;
              if (!checkList.value.includes(props.defaultWMS)) {
                checkList.value.push(layerList.value[index].layerName);
              }
              break;
            }
          }
        }
        if (!flg) {
          showChangeTC.value = !showChangeTC.value;
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  } else {
    if (!flg) {
      showChangeTC.value = !showChangeTC.value;
    }
  }
};

const changeLayer = (val) => {
  map.layers.items.forEach((v) => {
    if (v.type == 'wms') {
      v.visible = false;
      nextTick(() => {
        map.remove(v);
      });
    }
  });
  val.forEach((v) => {
    const index = v.indexOf(':');
    const title = v.substring(index + 1, v.length);
    const url = `${base.value}${v.substring(0, index)}/${title}/wms`;
    initWMS(url, title);
  });
};

//加载wms图层
const initWMS = (url, title) => {
  loadModules(['esri/layers/WMSLayer'], config).then(([WMSLayer]) => {
    const oneLayer = new WMSLayer({
      url: url,
      visible: true,
      id: title,
      // WMSLayer 自定义参数，由于后台geoserver增加了权限
      customParameters: {
        token: getToken()
      }
    });
    map.add(oneLayer);
    // oneLayer.on('layerview-create', function (event) {
    //   // The LayerView for the layer that emitted this event
    //   view.center = [oneLayer.fullExtent.center.longitude, oneLayer.fullExtent.center.latitude];
    //   view.zoom = 15;
    // });
  });
};

const executeIdentify = async (event: any) => {
  try {
    const [projection] = await loadModules(['esri/geometry/projection'], config);

    const element = document.getElementById('layerMapRef');
    if (!element) return;

    const offsetWidth = element.offsetWidth;
    const offsetHeight = element.offsetHeight;

    // 设置属性框位置
    top.value = event.y + 'px';
    left.value = event.x + 'px';

    // 确保属性框不超出地图范围
    if (event.x + 300 >= offsetWidth) {
      // 超出宽度 移到左边
      left.value = event.x - 300 + 'px';
    }
    if (event.y + 400 >= offsetHeight) {
      // 超出高度 移到上面
      top.value = event.y - 400 + 'px';
    }

    await projection.load();

    let hasFeatures = false; // 标记是否找到要素

    // 遍历所有选中的图层
    for (let index = 0; index < nowLayerList.value.length; index++) {
      const BBOX = `${event.mapPoint.x},${event.mapPoint.y},${event.mapPoint.x + tolerance},${event.mapPoint.y + tolerance}`;
      const layerName = nowLayerList.value[index];
      const num = layerName.indexOf(':');
      const title = layerName.substring(num + 1, layerName.length);
      //
      const url = `${base.value}${layerName.substring(0, num)}/${title}/wms?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetFeatureInfo&FORMAT=image%2Fpng&TRANSPARENT=true&QUERY_LAYERS=${layerName.substring(0, num)}%3A${title}&STYLES&LAYERS=${layerName.substring(0, num)}%3A${title}&exceptions=application%2Fvnd.ogc.se_inimage&INFO_FORMAT=application%2Fjson&FEATURE_COUNT=50&X=50&Y=50&SRS=EPSG%3A3857&WIDTH=101&HEIGHT=101&BBOX=${BBOX}&token=${getToken()}`;
      const res = await axios.get(url);
      if (res.data.features && res.data.features.length > 0) {
        hasFeatures = true;
        const featProps = res.data.features[0].properties;
        properties.value = [];

        // 将属性转换为数组格式
        Object.keys(featProps).forEach((key) => {
          properties.value.push({
            label: key,
            value: featProps[key]
          });
        });

        showAttr.value = true;
        break; // 找到要素后退出循环
      }
    }

    // 如果没有找到任何要素，关闭属性框
    if (!hasFeatures) {
      showAttr.value = false;
    }
  } catch (error) {
    showAttr.value = false; // 发生错误时也关闭属性框
  }
};

// 根据url获取属性
const getFeatures = (url) => {
  axios.get(url).then((res) => {
    if (res.data.features) {
      if (res.data.features.length != 0) {
        const propertiesTemp = res.data.features[0].properties;
        properties.value = [];
        const list = Object.keys(properties);
        list.forEach((v) => {
          properties.value.push({
            label: v,
            value: propertiesTemp[v]
          });
        });
        showAttr.value = true;
      }
    }
  });
};
/**
 * 居中某个图层
 * @param {*} item 图层
 */
const centerTc = (item) => {
  map.layers.items.forEach((v) => {
    if (v.type == 'wms' && v.id == item.title) {
      view.center = [v.fullExtent.center.longitude, v.fullExtent.center.latitude];
    }
  });
};

/**
 * 查看数据大屏
 */
const jumpScreen = async () => {
  const moduleId = projectStore.proModuleId;
  const searchMsg = {
    pageNo: 1,
    pageSize: 10000,
    moduleId: moduleId
  };
  if (moduleId) {
    getScreenList(searchMsg).then(async (res) => {
      if (res.code == 200) {
        if (res.data.records.length == 0) {
          //没有数据大屏的时候直接调用添加一个默认数据大屏
          const bigDataJson = JSON.parse(JSON.stringify(taskDefaultBigData));
          bigDataJson.moduleId = moduleId;
          const code = await selectModuleById();
          bigDataJson.components.forEach((v) => {
            if (v.cptOption.cptDataForm && v.cptOption.cptDataForm.dataSource == 2) {
              //是表达式的时候需要修改绑定的code
              v.cptOption.cptDataForm.code = code;
            }
            if (v.cptKey == 'cpt-select') {
              //任务组件单独处理
              v.cptOption.cptDataForm.moduleId = moduleId;
            }
          });
          bigDataJson.components = JSON.stringify(bigDataJson.components);
          bigDataJson.id = ''; //id置空
          bigDataJson.isDefault = 1; //默认模板
          saveScreen(bigDataJson).then((res) => {
            if (res.code == 200) {
              window.open(`/preview/${moduleId}/${res.data.id}`);
            } else {
              ElMessage.error(res.msg);
            }
          });
        }
        if (res.data.records.length != 0) {
          const id = res.data.records[0].id;
          window.open(`/preview/${moduleId}/${id}`);
        }
      } else {
        ElMessage.error(res.msg);
      }
    });
  }
};

// 图层要素的时候在列表选中某条数据 地图显示属性并高亮绘制
const drawWMSItem = (item) => {
  // 绘制前，需要把layer的图形全部移除
  graphicsLayerAll.removeAll();
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/geometry/Polygon'], config).then(([jsonUtils, Graphic, Polygon]) => {
    const bgColor = hexToRgba('#FF0000', '0.2');
    const polygon = new Polygon({
      rings: item.geometry.coordinates[0],
      spatialReference: { wkid: 102100 }
    });
    const simpleFillSymbol = {
      type: 'simple-fill',
      // color: [240,230,140, 0.8],  // Orange, opacity 80%
      color: bgColor,
      outline: {
        // color: [255, 255, 255],
        color: '#FF0000',
        width: 1
      }
    };
    const polygonGraphic = new Graphic({
      geometry: polygon,
      symbol: simpleFillSymbol
    });
    graphicsLayerAll.add(polygonGraphic);
    view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
    const heightGraphics = graphicsLayerAll.graphics.items[0];
    if (oldHighlight.value) {
      oldHighlight.value.remove(); //取消上一个高亮图形
    }
    view.whenLayerView(heightGraphics.layer).then(function (layerView) {
      oldHighlight.value = layerView.highlight(heightGraphics);
    });
    // 显示当前选中图形的属性
    const properties = item.properties;
    properties.value = [];
    const list = Object.keys(properties);
    list.forEach((v) => {
      properties.value.push({
        label: v,
        value: properties[v]
      });
    });
    top.value = window.innerHeight / 2 - 40 + 'px';
    left.value = window.innerWidth / 2 - 100 + 'px';
    showAttr.value = true;
  });
};

const cesTC = () => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    const geomArcgis =
      '{"rings":[[[[11745849.69162435,3185500.408744505],[11745849.575494783,3185500.388103385],[11745850.420166042,3185505.0027461196],[11745850.232074318,3185500.643178677],[11745849.69162435,3185500.408744505]]],[[[11745868.283269025,3185513.353540648],[11745868.246289019,3185512.3946984815],[11745852.10739604,3185514.220490393],[11745853.48643758,3185521.7545177117],[11745869.033422332,3185518.137901425],[11745868.283269025,3185513.353540648]]]],"spatialReference":{"latestWkid":3857,"wkid":102100}}';
    const polygon = jsonUtils.fromJSON(JSON.parse(geomArcgis));
    const simpleFillSymbol = {
      type: 'simple-fill',
      color: [240, 230, 140, 0.8], // Orange, opacity 80%
      // color:bgColor,
      outline: {
        color: [255, 255, 255],
        // color: v.styleAttribution.polylineColor,
        width: 1,
        style: 'solid'
      }
    };
    const polygonGraphic = new Graphic({
      geometry: polygon,
      symbol: simpleFillSymbol
    });
    view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
    view.zoom = 18;
    graphicsLayerAll.add(polygonGraphic);
  });
};

// 绘制标注
const drawLabel = (list) => {
  // 先移除之前的 数据
  labelLayer.removeAll();
  loadModules(
    ['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol', 'esri/geometry/Point', 'esri/geometry/SpatialReference'],
    config
  ).then(([jsonUtils, Graphic, TextSymbol, Point, SpatialReference]) => {
    list.forEach((v, idx) => {
      if (v.geomArcgis) {
        const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
        //创建文本符号
        let textSymbol = new TextSymbol();
        //文本的内容的实在
        textSymbol = {
          type: 'text',
          text: v.linkIdValue,
          color: '#ffa700',
          haloSize: '1px',
          xoffset: 0,
          yoffset: 0,
          font: {
            size: 14, // 这里设置字体大小，单位通常是像素
            weight: 'bold'
          }
        };
        let point = null;
        if (polygon.type == 'polygon') {
          point = new Point(polygon.centroid.x, polygon.centroid.y, new SpatialReference({ wkid: 102100 }));
        } else if (polygon.type == 'point') {
          //点
          point = polygon;
        } else if (polygon.type == 'polyline') {
          //线
          point = new Point(polygon.extent.center.x, polygon.extent.center.y, new SpatialReference({ wkid: 102100 }));
        }
        const graphic = new Graphic({
          geometry: point,
          symbol: textSymbol
        });
        labelLayer.add(graphic);
      }
    });
  });
};

/**
 * 当前高亮图形绘制拆迁高亮 一定是子要素图形
 * @param {*} msg
 * @param {*} id
 * @param {*} name
 * @param {*} isMainNode isMainNode 代表是根 根需要去graphicsLayer里面找
 */
const drawCQLabel = (msg, id, name, isMainNode) => {
  // 先移除之前的 数据
  loadModules(
    ['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol', 'esri/geometry/Point', 'esri/geometry/SpatialReference'],
    config
  ).then(([jsonUtils, Graphic, TextSymbol, Point, SpatialReference]) => {
    if (isMainNode) {
      for (let index = 0; index < graphicsLayerAll.graphics.items.length; index++) {
        if (graphicsLayerAll.graphics.items[index].id == id) {
          const polygon = graphicsLayerAll.graphics.items[index].geometry;
          nowPoint.value = new Point(polygon.centroid.x, polygon.centroid.y, new SpatialReference({ wkid: 102100 }));
          showCQPlan.value = true;
          msgMsg.value = msg;
          parcelNameZ.value = name;
          updateCQPlan();
          break;
        }
      }
    } else {
      for (let index = 0; index < childGraphics.graphics.items.length; index++) {
        if (childGraphics.graphics.items[index].id == id) {
          const polygon = childGraphics.graphics.items[index].geometry;
          nowPoint.value = new Point(polygon.centroid.x, polygon.centroid.y, new SpatialReference({ wkid: 102100 }));
          showCQPlan.value = true;
          msgMsg.value = msg;
          parcelNameZ.value = name;
          updateCQPlan();
          break;
        }
      }
    }
  });
};

const changeJZDVisbale = (flg) => {
  if (flg) {
    //显示界址点标注
    fontLayer.visible = true;
  } else {
    fontLayer.visible = false;
  }
};

// 获取模块信息
const selectModuleById = () => {
  return new Promise((resolve, reject) => {
    const moduleId = projectStore.proModuleId;
    selectModuleByIdApi({ id: moduleId }).then((res: any) => {
      if (res.code == 200) {
        resolve(res.data.code);
      } else {
        ElMessage.error(res.msg);
      }
    });
  });
};

/**
 * 移除标注
 */
const removerLabelLayer = () => {
  labelLayer.removeAll();
};

/**
 * 新增的时候还是打开新的窗口做
 */
const addProject = () => {
  router.push(`/addProject/index/${projectStore.proModuleId}`);
};

/**
 * 获取拆迁进度颜色
 * @param {*} item
 * @returns
 */
const getCQPlanClass = (item) => {
  let obj = {};
  switch (item.type) {
    case 1:
      obj = { 'color1': true };
      break;
    case 2:
      obj = { 'color2': true };
      break;
    case 3:
      obj = { 'color3': true };
      break;
    case 4:
      obj = { 'color4': true };
      break;
    case 5:
      obj = { 'color5': true };
      break;
    case 6:
      obj = { 'color6': true };
      break;
    case 7:
      obj = { 'color7': true };
      break;
    default:
      break;
  }
  return obj;
};

const closeTL = () => {
  showTL.value = false;
};

/**
 * 重新绘制  根据需要绘制的内容重新绘制图形
 * @param {*} parcelList
 * @param {*} showGraphs
 */
const reloadGraphs = (parcelList, showGraphs) => {
  loadModules(['esri/layers/GraphicsLayer'], config).then(([GraphicsLayer]) => {
    // 未知原因 无法直接调用removeAll()清除图形
    map.remove(graphicsLayerAll);
    //添加宗地图形layer到map
    graphicsLayerAll = new GraphicsLayer({
      id: '123'
    });
    map.add(graphicsLayerAll);
    if (showGraphs.length != 0) {
      reloadGraphsItem(parcelList, showGraphs);
    }
  });
};
/**
 * 重新绘制  根据需要绘制的内容重新绘制图形
 * @param {*} parcelList
 * @param {*} showGraphs
 */
const reloadGraphsItem = (parcelList, showGraphs) => {
  loadModules(['esri/geometry/support/jsonUtils', 'esri/Graphic', 'esri/symbols/TextSymbol'], config).then(([jsonUtils, Graphic, TextSymbol]) => {
    parcelList.forEach((v, idx) => {
      if (v.geomArcgis && showGraphs.includes(v.ruleId)) {
        let bgColor = null;
        if (v.styleAttribution.polylineColor.includes('rgba')) {
          bgColor = v.styleAttribution.polylineColor;
        } else {
          bgColor = hexToRgba(v.styleAttribution.polylineColor, '0.2');
          if (v.styleAttribution.polygonFillColor) {
            bgColor = hexToRgba(v.styleAttribution.polygonFillColor);
          }
        }
        const polygon = jsonUtils.fromJSON(JSON.parse(v.geomArcgis));
        if (v.graphicalType == 1) {
          //点
          //创建文本符号
          let textSymbol = new TextSymbol();

          textSymbol = {
            type: 'simple-marker', // autocasts as SimpleLineSymbol()
            color: v.styleAttribution.pointColor,
            width: v.styleAttribution.pointSize || 2
          };
          const graphic = new Graphic({
            geometry: polygon,
            symbol: textSymbol,
            id: v.id
          });
          if (idx == 0) {
            //定位第一个
            view.center = [polygon.longitude, polygon.latitude];
            view.zoom = 18;
          }
          graphicsLayerAll.add(graphic);
        } else if (v.graphicalType == 2) {
          //线
          const lineSymbol = {
            type: 'simple-line', // autocasts as new SimpleLineSymbol()
            color: v.styleAttribution.polylineColor, // RGB color values as an array
            width: v.styleAttribution.polylineWidth || 1,
            style: v.styleAttribution.polylineType
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: lineSymbol,
            id: v.id
          });
          if (idx == 0) {
            //定位第一个
            view.center = [polygon.extent.center.longitude, polygon.extent.center.latitude];
            view.zoom = 18;
          }
          graphicsLayerAll.add(polygonGraphic);
        } else if (v.graphicalType == 3) {
          //面
          const simpleFillSymbol = {
            type: 'simple-fill',
            // color: [240,230,140, 0.8],  // Orange, opacity 80%
            // color:bgColor,
            outline: {
              // color: [255, 255, 255],
              color: v.styleAttribution.polylineColor,
              width: v.styleAttribution.polylineWidth || 1,
              style: v.styleAttribution.polylineType || 'solid'
            }
          };
          const polygonGraphic = new Graphic({
            geometry: polygon,
            symbol: simpleFillSymbol,
            id: v.id
          });
          if (reloadFirst.value) {
            view.center = [polygonGraphic.geometry.centroid.longitude, polygonGraphic.geometry.centroid.latitude];
            view.zoom = 18;
            reloadFirst.value = false;
          }

          graphicsLayerAll.add(polygonGraphic);
        }
      }
      if (v.list.length != 0) {
        reloadGraphsItem(v.list, showGraphs);
      }
    });
  });
};
defineExpose({
  init,
  initLinye,
  endChangHighLight,
  clearDeg,
  drawLabel,
  changeJZDVisbale,
  clearCQLabel,
  initLableCQ,
  reloadGraphs,
  initDeg,
  removerLabelLayer
});
// --- 生命周期 ---
onBeforeUnmount(() => {
  if (view) {
    // destroy the map view
    view.container = null;
  }
});
</script>

<style scoped lang="scss">
.right-map-color {
  position: absolute;
  right: 60px;
  bottom: 50px;
  width: 260px;
  border: #409eff solid 1px;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  font-size: 12px;
  .title {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 10px;
    .right-close {
      height: 16px;
      width: 16px;
      border: #87ceeb solid 1px;
      color: #409eff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
  .hr {
    border-top: #87ceeb solid 1px;
    width: 100%;
  }
  .tuli-content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 10px;
    .item {
      width: calc(50% - 0px);
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      .color-box {
        width: 40px;
        height: 20px;
        margin-left: 2px;
      }
      .color1 {
        background: #0199ff;
      }
      .color2 {
        background: #0002cc;
      }
      .color3 {
        background: #9b66fd;
      }
      .color4 {
        background: #feff02;
      }
      .color5 {
        background: #00ff80;
      }
      .color6 {
        background: #ff01fd;
      }
      .color7 {
        background: #ff8d02;
      }
    }
  }
}
.cqplan-div {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  .cqplan-title {
    padding: 5px 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #fff;
    background: linear-gradient(107deg, rgba(3, 59, 146, 0.5) -8%, rgba(0, 34, 87, 0.5) 99%);
    border: 1px solid #7ab8ff;
    backdrop-filter: blur(10px);
    border-radius: 5px;
  }
  .hr {
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 12px solid rgba(0, 34, 87, 0.6); /* 可以修改颜色和大小 */
    // background: rgba(0, 34, 87, .6);
  }
  .cqplan-content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .item {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 14px;
      padding: 5px;
      border: #fff solid 1px;
    }
    .cqOk {
      background: #058828;
    }
    .cqON {
      background: #dc1a35;
    }
  }
}
.handle-top {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 8px;
  background: rgb(0, 0, 0, 0.5);
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  align-items: center;
  .pick-up {
    width: 16px;
    height: 16px;
    cursor: pointer;
    color: #fff;
  }
  .pick-up:hover {
    color: #1890ff;
  }
  .handle-item {
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  .handle-item:hover {
    color: #1890ff;
  }
  .hr {
    height: 16px;
    border-right: #fff solid 1px;
    margin: 0px 10px;
  }
}
:deep(.esri-view .esri-view-surface:focus::after) {
  outline: none !important;
}
:deep(.esri-scale-bar__line--top) {
  background-color: transparent;
  border-bottom: 2px solid #fff;
}
:deep(.el-table th.el-table__cell) {
  background-color: #e5e5e5;
}
:deep(.esri-scale-bar__label) {
  color: #fff;
}
:deep(.esri-scale-bar__line--top:after) {
  border-right: 2px solid #fff;
}
:deep(.esri-scale-bar__line--top:before) {
  border-right: 2px solid #fff;
}
:deep(.esri-compass) {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
}
:deep(.esri-ui-bottom-right) {
  display: flex;
  flex-direction: column;
}
:deep(.esri-zoom) {
  margin-top: 16px;
}
:deep(.esri-widget) {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px 8px 8px 8px;
}
:deep(.esri-zoom .esri-widget--button) {
  background-color: transparent !important;
  color: #fff !important;
}
:deep(.esri-zoom .esri-widget--button:hover) {
  background-color: #fff;
  color: #000;
}
:deep(.esri-ui-bottom-right) {
  bottom: 10px;
}
:deep(.esri-widget--button) {
  background-color: transparent !important;
  color: #fff !important;
}

.gisMap-main {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 0;
  .check-screen {
    position: absolute;
    right: 16px;
    top: 25px;
    padding: 5px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    color: #fff;
    font-size: 12px;
  }
  .check-screen1 {
    position: absolute;
    right: 16px;
    top: 45px;
    padding: 5px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    color: #fff;
    font-size: 12px;
  }
  .map {
    width: 100%;
    height: 100%;
  }

  .rght-handle-div {
    position: absolute;
    width: 20px;
    height: 36px;
    background: #f1efef;
    border-radius: 4px 0px 0px 4px;
    right: 0px;
    top: 45%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #999;
    z-index: 99;

    .zankai {
      width: 16px;
      height: 16px;
    }
  }

  .rght-handle-div :hover {
    color: #1b9af7;
  }

  .project-detail {
    position: absolute;
    width: 500px;
    height: calc(100% - 30px);
    background: #ffffff;
    right: 0px;
    top: 0px;
    padding: 15px;
    // opacity: .8;
    .head-div {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }

    .tab-div {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      overflow-x: auto;

      .tab-row {
        height: 30px;
        min-width: 110px;
        font-size: 14px;
        color: #999999;
        cursor: pointer;
        background-color: #f6f7f8;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .tab-active {
        background: #e7f5ff;
        color: #333333;
      }

      .error-tab {
        border: #ff5555 solid 1px;
      }
    }

    .detail-content {
      height: calc(100% - 123px);
      overflow: auto;

      .flex-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;

        .item {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .label {
            color: #333333;
            font-size: 14px;
            margin-bottom: 15px;
          }
        }

        .margin {
          margin-left: 20px;
        }
      }

      .flex-box {
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 10px;
        margin-bottom: 20px;

        .flex-row {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;

          .item {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .label {
              color: #333333;
              font-size: 14px;
              margin-bottom: 15px;
            }

            .img-box {
              width: 100%;
              height: 140px;
            }
          }

          .margin {
            margin-left: 20px;
          }
        }

        .right-handle {
          display: flex;
          flex-basis: row;
          justify-content: flex-end;

          .span {
            color: #999999;
            cursor: pointer;
          }
        }
      }

      .grid-container {
        display: grid;
        grid-gap: 20px 20px;
        grid-template-columns: repeat(2, 45%);
        width: 100%;

        .grid-item {
          height: 300px;
        }
      }
    }
  }

  .more-right {
    right: 500px;
  }

  .dialog-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    .label {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .content {
    }
  }

  .zdfclabel {
    display: inline-block;
    width: 20%;
  }

  .zdfccontent {
    display: inline-block;
    width: 70%;
  }
  .uploadimgspan {
    display: inline-block;
    font-size: 3pt;
    font-style: italic;
  }

  .dialog-box-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }

  .dialog-row-item {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    // justify-content: space-between;
    margin-bottom: 20px;

    .checkbox-item {
      width: 20%;
      margin-bottom: 5px;
    }
  }

  .dialog-label {
    margin-bottom: 10px;
  }

  .dialog-row-right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }

  .red-span {
    color: #ff5555;
    margin-bottom: 10px;
  }

  .zhipai {
    position: absolute;
    top: 70px;
    left: 15px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    width: 64px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
  }

  .gengxin {
    position: absolute;
    top: 110px;
    left: 15px;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    width: 64px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
  }
  .right-handle-div {
    position: absolute;
    right: 16px;
    bottom: 170px;
    .tuceng-box {
      position: absolute;
      max-height: 200px;
      background: rgba(0, 0, 0, 0.5);
      right: 45px;
      border-radius: 8px;
      overflow: auto;
      padding: 5px;
      top: 0px;
      min-width: 200px;
      color: #fff;
      .check-item {
        margin-bottom: 5px;
        color: #fff;
      }
    }
    /*滚动条样式*/
    .tuceng-box::-webkit-scrollbar {
      width: 4px;
    }
    .tuceng-box::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: rgb(255, 255, 255, 0.5);
    }
    .tuceng-box::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: rgba(248, 248, 248, 0.1);
    }
    .min-handle-item {
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #6a6a6a;
      position: relative;
    }
    .min-handle-item:hover {
      color: #000;
      background: #fff;
    }
    .handle-marign {
      margin-top: 12px;
    }
    .handle-marign-active {
      color: #000;
      background: #fff;
    }
  }
  .map-change {
    position: absolute;
    bottom: 180px;
    right: 60px;
    display: flex;
    flex-direction: row;
    .map-item {
      width: 96px;
      height: 72px;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: flex-end;
      .map-footer {
        width: 100%;
        height: 22px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #161d26 100%);
        border-radius: 0px 0px 8px 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #fff;
      }
      .map-footer-active {
        color: #1b9af7;
      }
    }
    .map-left {
      background-image: url('../../../assets/images/normal-map.png');
      background-size: cover;
    }
    .map-right {
      background-image: url('../../../assets/images/image-map.png');
      background-size: cover;
      margin-left: 8px;
    }
    .map-active {
      border: #1b9af7 solid 1px;
    }
  }
  .map-copyRight {
    position: absolute;
    bottom: 10px;
    left: 16px;
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 12px;
    .copy-ico {
      width: 53px;
      height: 22px;
      margin-right: 10px;
    }
  }
}
.attr-box {
  position: absolute;
  width: 300px;
  height: 400px;
  z-index: 1;
  .attr-div {
    width: 100%;
    height: 100%;
    background: rgb(0, 0, 0, 0.5);
    border-radius: 8px;
    position: relative;
    overflow: auto;
    .attr-close {
      position: absolute;
      top: 8px;
      right: 5px;
      font-size: 16px;
      color: #fff;
      cursor: pointer;
    }
    .attr-row {
      height: 32px;
      display: flex;
      flex-direction: row;
      align-items: center;
      color: #fff;
      padding: 0px 10px;
      flex-wrap: wrap;
      font-size: 12px;
      .left {
        width: 100px;
      }
      .right {
        flex: 1;
      }
    }
    .two {
      background: rgba(0, 0, 0, 0.5);
    }
    .title {
      font-weight: bold;
      display: flex;
      justify-content: center;
      background: rgba(0, 0, 0, 0.5);
      border-top-right-radius: 8px;
      border-top-left-radius: 8px;
    }
  }
  /*滚动条样式*/
  .attr-div::-webkit-scrollbar {
    width: 4px;
  }
  .attr-div::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(176, 175, 175, 0.5);
  }
  .attr-div::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(248, 248, 248, 0.1);
  }
}

:deep(.el-tree) {
  background-color: transparent;
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background-color: rgba(248, 248, 248, 0.2);
  }
}
</style>

<style>
.upload-demo .el-upload-list--picture-card .el-upload-list__item {
  width: 110px;
  height: 110px;
}
.upload-demo .el-upload-list--picture-card img {
  width: 110px;
  height: 110px;
}
.uoloadSty .el-upload--picture-card {
  width: 110px;
  height: 110px;
  line-height: 110px;
}

.disUoloadSty .el-upload--picture-card {
  display: none;
  /* 上传按钮隐藏 */
}
</style>
